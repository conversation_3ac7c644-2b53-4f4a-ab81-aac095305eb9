-- Bookmarqd Seed Data
-- Insert default folders and sample bookmarks

-- Helper function to create default folders for a user
CREATE OR REPLACE FUNCTION create_default_folders(user_uuid UUID)
RETURNS void AS $$
BEGIN
  -- Insert default folders
  INSERT INTO folders (user_id, name, description, color, icon, is_default) VALUES
  (user_uuid, 'Ideas', 'Creative ideas and inspiration', '#FF4500', '💡', true),
  (user_uuid, 'Goals', 'Personal and professional goals', '#FF6B35', '🎯', true),
  (user_uuid, 'Helpful Tips', 'Useful tips and advice', '#FF8C42', '📝', true);
END;
$$ LANGUAGE plpgsql;

-- Sample bookmarks (replace with actual user ID when testing)
-- This would typically be called after user registration
/*
INSERT INTO bookmarks (user_id, folder_id, type, url, title, platform, ai_summary, ai_tags) VALUES
(
  auth.uid(), 
  (SELECT id FROM folders WHERE name = 'Ideas' AND user_id = auth.uid()), 
  'url', 
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 
  'Creative Design Thinking Process', 
  'youtube', 
  'Video explaining design thinking methodology and creative problem-solving techniques',
  ARRAY['design', 'creativity', 'process', 'methodology']
),
(
  auth.uid(), 
  (SELECT id FROM folders WHERE name = 'Goals' AND user_id = auth.uid()), 
  'url', 
  'https://reddit.com/r/productivity/posts/goal-setting', 
  'Effective Goal Setting Strategies', 
  'reddit', 
  'Reddit discussion about SMART goals and productivity techniques for achieving objectives',
  ARRAY['goals', 'productivity', 'planning', 'success']
),
(
  auth.uid(), 
  (SELECT id FROM folders WHERE name = 'Helpful Tips' AND user_id = auth.uid()), 
  'url', 
  'https://twitter.com/productivityguru/status/123456789', 
  'Time Management Thread', 
  'twitter', 
  'Twitter thread with 10 practical time management tips for busy professionals',
  ARRAY['time-management', 'productivity', 'tips', 'efficiency']
);
*/