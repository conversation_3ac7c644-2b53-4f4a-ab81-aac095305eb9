{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@bookmarqd/shared": ["./packages/shared/src"], "@bookmarqd/shared/*": ["./packages/shared/src/*"]}}, "include": ["packages/**/*", "apps/**/*", "*.ts", "*.js"], "exclude": ["node_modules", "dist", "build", "**/*.spec.ts", "**/*.test.ts"]}