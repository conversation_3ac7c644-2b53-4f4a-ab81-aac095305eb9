# 🚀 Bookmarqd Quick Start Guide

## ⚡ Fast Setup (5 minutes)

### 1. 📋 Prerequisites
- Node.js 18+ and npm 9+
- Supabase account ([signup here](https://supabase.com))
- Expo CLI (for mobile): `npm install -g @expo/cli`

### 2. 🔧 Environment Setup
```bash
# Copy environment template
npm run setup:env

# Edit .env file with your Supabase credentials
# (Get these from your Supabase dashboard)
```

### 3. 🗄️ Database Setup
```bash
# Install dependencies (if not done already)
npm install

# Run automated database setup
npm run setup:db
```

### 4. 📱 Start Development

**Mobile App:**
```bash
cd apps/mobile
npm run start
# Scan QR code with Expo Go app or run in simulator
```

**Chrome Extension:**
```bash
cd apps/extension
npm run build
# Load the dist/ folder in Chrome Extensions (Developer mode)
```

---

## 📖 Detailed Setup Instructions

### 🎯 Step 1: Supabase Project Setup

1. **Create Supabase Project:**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Note your project URL and API keys

2. **Get Your Credentials:**
   ```
   Project URL: https://your-project-ref.supabase.co
   Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   Service Role Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... (Settings > API)
   ```

### 🎯 Step 2: Configure Environment

1. **Create .env file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit .env with your credentials:**
   ```env
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_ANON_KEY=your_actual_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_actual_service_key
   
   EXPO_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key
   ```

### 🎯 Step 3: Database & Storage Setup

**Automated Setup (Recommended):**
```bash
npm run setup:db
```

**Manual Setup (if needed):**
1. Go to Supabase SQL Editor
2. Copy/paste contents of `supabase-schema.sql`
3. Run the SQL script
4. Go to Storage → Create buckets:
   - `bookmarks` (public read, auth write)
   - `avatars` (public read, auth write)

### 🎯 Step 4: Development

**Start everything:**
```bash
npm run dev  # Starts both mobile and extension dev servers
```

**Or start individually:**
```bash
# Mobile app
npm run dev:mobile

# Extension
npm run dev:extension
```

---

## 📱 Mobile App Usage

1. **Run the app:**
   ```bash
   cd apps/mobile
   npm run start
   ```

2. **Test on device:**
   - Install **Expo Go** app
   - Scan QR code from terminal
   - Sign up/Login to test authentication

3. **Key features to test:**
   - ✅ Onboarding flow
   - ✅ Sign up/Login
   - ✅ Home screen with moodboard
   - ✅ Folder management
   - ✅ Add bookmark functionality

---

## 🔗 Chrome Extension Usage

1. **Build the extension:**
   ```bash
   cd apps/extension
   npm run build
   ```

2. **Load in Chrome:**
   - Open `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select `apps/extension/dist/` folder

3. **Test features:**
   - ✅ Click extension icon for popup
   - ✅ Right-click → "Save to Bookmarqd"
   - ✅ Visit YouTube/Reddit/Twitter for auto-detection
   - ✅ Save bookmarks to folders

---

## 🧪 Testing & Verification

### Database Test
```bash
# Check if tables were created
# Go to Supabase → Table Editor
# You should see: folders, bookmarks tables
```

### Authentication Test
```bash
# Try signing up in mobile app
# Check Supabase → Authentication → Users
# New user should appear with default folders
```

### Storage Test
```bash
# Check Supabase → Storage
# You should see: bookmarks, avatars buckets
```

---

## 🔧 Available Scripts

```bash
# Environment
npm run setup:env          # Copy .env template
npm run setup:db           # Setup database automatically

# Development  
npm run dev               # Start mobile + extension
npm run dev:mobile        # Start mobile app only
npm run dev:extension     # Start extension dev build

# Building
npm run build             # Build everything
npm run build:mobile      # Build mobile app
npm run build:extension   # Build extension

# Utilities
npm run type-check        # TypeScript type checking
npm run lint              # Code linting
npm run test              # Run tests
npm run clean             # Clean all dependencies
```

---

## 🚨 Troubleshooting

### Common Issues:

**1. "Cannot connect to Supabase"**
- ✅ Check .env credentials are correct
- ✅ Verify Supabase project is active
- ✅ Ensure no trailing spaces in .env values

**2. "Database tables not found"**
- ✅ Run `npm run setup:db`
- ✅ Or manually execute `supabase-schema.sql`

**3. "Extension not loading"**
- ✅ Build with `npm run build:extension`
- ✅ Ensure Chrome Developer mode is enabled
- ✅ Check console for errors

**4. "Mobile app won't start"**
- ✅ Install Expo CLI: `npm install -g @expo/cli`
- ✅ Clear cache: `npx expo start --clear`
- ✅ Check Node.js version (18+ required)

**5. "Authentication not working"**
- ✅ Check Supabase Auth settings
- ✅ Verify RLS policies are enabled
- ✅ Test with simple email/password first

---

## 📞 Next Steps

Once setup is complete:

1. **🎨 Customize branding** in `packages/shared/src/constants/`
2. **🤖 Add AI features** (Gemini API integration)
3. **📊 Add analytics** and error tracking
4. **🚀 Deploy mobile app** with EAS Build
5. **📦 Package extension** for Chrome Web Store

---

## 🎉 You're Ready!

Your Bookmarqd development environment is now fully configured! Start building your AI-powered bookmark manager. 

**Happy coding! 🚀**