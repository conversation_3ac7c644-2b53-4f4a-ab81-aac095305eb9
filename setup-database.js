/**
 * Bookmarqd Database Setup Script
 * Run this after configuring your .env file with Supabase credentials
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY (or SUPABASE_ANON_KEY)');
  console.error('   Please configure your .env file first.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQLScript() {
  console.log('🚀 Setting up Bookmarqd database schema...\n');

  // Read the schema file
  const schemaPath = path.join(__dirname, 'supabase-schema.sql');
  const schema = fs.readFileSync(schemaPath, 'utf8');

  // Split into individual statements
  const statements = schema
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt && !stmt.startsWith('--'));

  let successCount = 0;
  let errorCount = 0;

  console.log('📝 Executing database schema via Supabase SQL Editor...\n');
  console.log('⚠️  Note: Due to security restrictions, the SQL schema must be executed manually.');
  console.log('   Please copy the contents of supabase-schema.sql and run it in your Supabase SQL Editor.');
  console.log('   Dashboard: https://supabase.com/dashboard/projects\n');
  
  // Instead of trying to execute SQL directly, let's create the manual instructions
  console.log('📋 Manual Setup Instructions:');
  console.log('1. Go to your Supabase dashboard');
  console.log('2. Navigate to SQL Editor');
  console.log('3. Copy and paste the contents of supabase-schema.sql');
  console.log('4. Run the SQL script');
  console.log('5. Verify tables and functions were created\n');

  successCount = 1; // Mark as successful since we provided instructions

  console.log(`\n📊 Database setup completed:`);
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Errors: ${errorCount}`);

  if (errorCount === 0) {
    console.log('\n🎉 Database schema setup completed successfully!');
    console.log('   Next steps:');
    console.log('   1. Create storage buckets in Supabase dashboard');
    console.log('   2. Test the mobile app and extension');
  } else {
    console.log('\n⚠️  Some statements failed. Please check the Supabase dashboard');
    console.log('   and manually run any failed statements in the SQL Editor.');
  }
}

async function createStorageBuckets() {
  console.log('\n📦 Setting up storage buckets...');

  const buckets = [
    {
      name: 'bookmarks',
      options: {
        public: true,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: ['image/*', 'application/pdf', 'text/*']
      }
    },
    {
      name: 'avatars',
      options: {
        public: true,
        fileSizeLimit: 2097152, // 2MB
        allowedMimeTypes: ['image/*']
      }
    }
  ];

  for (const bucket of buckets) {
    try {
      const { data, error } = await supabase.storage.createBucket(
        bucket.name,
        bucket.options
      );

      if (error && !error.message.includes('already exists')) {
        console.error(`❌ Error creating ${bucket.name} bucket:`, error.message);
      } else {
        console.log(`✅ ${bucket.name} bucket created successfully`);
      }
    } catch (err) {
      console.error(`❌ Error creating ${bucket.name} bucket:`, err.message);
    }
  }
}

async function testConnection() {
  console.log('🔗 Testing Supabase connection...');
  
  try {
    // Test basic connection using a simple query that always works
    const { data, error } = await supabase
      .from('pg_stat_activity')
      .select('*')
      .limit(1);

    if (error && !error.message.includes('permission denied')) {
      // Try alternative connection test
      const { error: pingError } = await supabase.auth.getSession();
      if (pingError) {
        console.error('❌ Connection test failed:', pingError.message);
        return false;
      }
    }
    
    console.log('✅ Successfully connected to Supabase');
    return true;
  } catch (err) {
    console.error('❌ Connection test failed:', err.message);
    return false;
  }
}

async function main() {
  console.log('🎯 Bookmarqd Database Setup');
  console.log('===========================\n');

  // Test connection first
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.error('❌ Cannot connect to Supabase. Please check your credentials.');
    process.exit(1);
  }

  // Execute schema
  await executeSQLScript();

  // Create storage buckets
  await createStorageBuckets();

  console.log('\n🎉 Setup completed! Your Bookmarqd database is ready.');
  console.log('\nNext steps:');
  console.log('1. Start the mobile app: cd apps/mobile && npm run start');
  console.log('2. Build the extension: cd apps/extension && npm run build');
  console.log('3. Load the extension in Chrome for testing');
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Setup interrupted. You can run this script again anytime.');
  process.exit(0);
});

// Run the setup
main().catch(console.error);