import { Platform } from '../types/database';
import { PLATFORM_CONFIG } from '../constants';

export function detectPlatform(url: string): Platform | null {
  for (const [platform, config] of Object.entries(PLATFORM_CONFIG)) {
    for (const pattern of config.urlPatterns) {
      if (pattern.test(url)) {
        return platform as Platform;
      }
    }
  }
  return null;
}

export function getPlatformConfig(platform: Platform) {
  return PLATFORM_CONFIG[platform];
}

export function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/,
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/,
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[2] && match[2].length === 11) {
      return match[2];
    }
  }
  
  return null;
}

export function getYouTubeThumbnail(videoId: string): string {
  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
}