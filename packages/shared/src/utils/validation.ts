import { z } from 'zod';
import { LIMITS, SUPPORTED_FILE_TYPES } from '../constants';
import { BookmarkType, Platform, FileType } from '../types/database';

export const bookmarkTypeSchema = z.enum(['url', 'file', 'screenshot'] as const);
export const platformSchema = z.enum(['youtube', 'reddit', 'twitter', 'instagram', 'facebook', 'pinterest'] as const);
export const fileTypeSchema = z.enum(['pdf', 'image', 'document'] as const);

export const createBookmarkSchema = z.object({
  folder_id: z.string().uuid(),
  type: bookmarkTypeSchema,
  url: z.string().url().optional(),
  title: z.string().max(LIMITS.maxTitleLength).optional(),
  description: z.string().max(LIMITS.maxDescriptionLength).optional(),
  platform: platformSchema.optional(),
  thumbnail_url: z.string().url().optional(),
  file_name: z.string().optional(),
  file_type: fileTypeSchema.optional(),
  user_tags: z.array(z.string().max(LIMITS.maxTagLength)).max(LIMITS.maxTagsPerBookmark).optional(),
  notes: z.string().max(LIMITS.maxNotesLength).optional(),
  is_favorite: z.boolean().optional(),
});

export const updateBookmarkSchema = z.object({
  id: z.string().uuid(),
  title: z.string().max(LIMITS.maxTitleLength).optional(),
  description: z.string().max(LIMITS.maxDescriptionLength).optional(),
  user_tags: z.array(z.string().max(LIMITS.maxTagLength)).max(LIMITS.maxTagsPerBookmark).optional(),
  notes: z.string().max(LIMITS.maxNotesLength).optional(),
  is_favorite: z.boolean().optional(),
  folder_id: z.string().uuid().optional(),
});

export const createFolderSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
  icon: z.string().max(10).optional(),
});

export const updateFolderSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
  icon: z.string().max(10).optional(),
});

export const searchBookmarksSchema = z.object({
  query: z.string().optional(),
  folder_id: z.string().uuid().optional(),
  platform: platformSchema.optional(),
  type: bookmarkTypeSchema.optional(),
  is_favorite: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function validateFileType(fileName: string): FileType | null {
  const ext = fileName.toLowerCase().split('.').pop();
  if (!ext) return null;
  
  const extWithDot = `.${ext}` as any;
  
  if (SUPPORTED_FILE_TYPES.images.includes(extWithDot)) {
    return 'image';
  }
  if (SUPPORTED_FILE_TYPES.documents.includes(extWithDot)) {
    if (ext === 'pdf') return 'pdf';
    return 'document';
  }
  
  return null;
}

export function validateFileSize(size: number): boolean {
  return size <= LIMITS.maxFileSize;
}