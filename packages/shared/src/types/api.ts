import { Bookmark, Folder, BookmarkType, Platform } from './database';

export interface CreateBookmarkRequest {
  folder_id: string;
  type: BookmarkType;
  url?: string;
  title?: string;
  description?: string;
  platform?: Platform;
  thumbnail_url?: string;
  file_name?: string;
  file_type?: string;
  user_tags?: string[];
  notes?: string;
  is_favorite?: boolean;
}

export interface UpdateBookmarkRequest {
  id: string;
  title?: string;
  description?: string;
  user_tags?: string[];
  notes?: string;
  is_favorite?: boolean;
  folder_id?: string;
}

export interface CreateFolderRequest {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
}

export interface UpdateFolderRequest {
  id: string;
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
}

export interface SearchBookmarksRequest {
  query?: string;
  folder_id?: string;
  platform?: Platform;
  type?: BookmarkType;
  is_favorite?: boolean;
  tags?: string[];
  limit?: number;
  offset?: number;
}

export interface SearchBookmarksResponse {
  bookmarks: Bookmark[];
  total: number;
  has_more: boolean;
}

export interface BookmarkWithFolder extends Bookmark {
  folder: Folder;
}

export interface AIProcessingRequest {
  content: string;
  type: 'url' | 'text' | 'image' | 'pdf';
  url?: string;
}

export interface AIProcessingResponse {
  summary?: string;
  tags?: string[];
  insights?: string;
  extracted_text?: string;
}