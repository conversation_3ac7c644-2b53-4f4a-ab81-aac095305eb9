export interface Database {
  public: {
    Tables: {
      folders: {
        Row: Folder;
        Insert: Omit<Folder, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Folder, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;
      };
      bookmarks: {
        Row: Bookmark;
        Insert: Omit<Bookmark, 'id' | 'created_at' | 'updated_at' | 'search_vector'>;
        Update: Partial<Omit<Bookmark, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'search_vector'>>;
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

export interface Folder {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export type BookmarkType = 'url' | 'file' | 'screenshot';
export type Platform = 'youtube' | 'reddit' | 'twitter' | 'instagram' | 'facebook' | 'pinterest';
export type FileType = 'pdf' | 'image' | 'document';

export interface Bookmark {
  id: string;
  user_id: string;
  folder_id: string;
  type: BookmarkType;
  
  // URL bookmark fields
  url?: string;
  title?: string;
  description?: string;
  platform?: Platform;
  thumbnail_url?: string;
  
  // File bookmark fields
  file_url?: string;
  file_name?: string;
  file_type?: FileType;
  file_size?: number;
  
  // AI-enhanced content
  ai_summary?: string;
  ai_tags?: string[];
  extracted_text?: string;
  ai_insights?: string;
  
  // User-added data
  user_tags?: string[];
  notes?: string;
  is_favorite: boolean;
  
  created_at: string;
  updated_at: string;
}