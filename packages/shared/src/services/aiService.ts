// AI Service using OpenRouter for Gemini 2.5 Flash Lite
export interface OpenRouterRequest {
  content: string;
  type: 'url' | 'text' | 'image' | 'pdf';
  url?: string;
}

export interface OpenRouterResponse {
  summary?: string;
  tags?: string[];
  insights?: string;
  extracted_text?: string;
}

export class AIService {
  private apiKey: string;
  private baseUrl = 'https://openrouter.ai/api/v1';
  private model = 'google/gemini-2.0-flash-exp:free'; // Gemini 2.5 Flash Lite via OpenRouter

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async processContent(request: OpenRouterRequest): Promise<OpenRouterResponse> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    try {
      const prompt = this.buildPrompt(request);
      const response = await this.callOpenRouter(prompt);
      return this.parseResponse(response);
    } catch (error) {
      console.error('AI processing error:', error);
      throw error;
    }
  }

  private buildPrompt(request: OpenRouterRequest): string {
    const basePrompt = `Analyze the following content and provide:
1. A concise summary (1-2 sentences)
2. Relevant tags (3-5 keywords)
3. Key insights or takeaways
4. If applicable, extracted text content

Content type: ${request.type}
${request.url ? `Source URL: ${request.url}` : ''}

Content:
${request.content}

Please respond in this JSON format:
{
  "summary": "Brief summary here",
  "tags": ["tag1", "tag2", "tag3"],
  "insights": "Key insights and takeaways",
  "extracted_text": "Any text content extracted"
}`;

    return basePrompt;
  }

  private async callOpenRouter(prompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'X-Title': 'Bookmarqd Content Processor'
      },
      body: JSON.stringify({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an AI assistant that helps organize and analyze bookmarked content. Provide concise, useful summaries and insights.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  private parseResponse(response: string): OpenRouterResponse {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(response);
      return {
        summary: parsed.summary || '',
        tags: Array.isArray(parsed.tags) ? parsed.tags : [],
        insights: parsed.insights || '',
        extracted_text: parsed.extracted_text || ''
      };
    } catch {
      // Fallback to text parsing if JSON parsing fails
      return {
        summary: response.substring(0, 200) + '...',
        tags: [],
        insights: response,
        extracted_text: ''
      };
    }
  }

  // Helper method for URL content analysis
  async analyzeUrl(url: string): Promise<OpenRouterResponse> {
    // This would typically fetch the URL content first
    // For now, we'll analyze just the URL structure
    return this.processContent({
      content: `Analyze this URL: ${url}`,
      type: 'url',
      url
    });
  }

  // Helper method for image OCR
  async extractTextFromImage(imageUrl: string): Promise<string> {
    const response = await this.processContent({
      content: `Extract text from this image: ${imageUrl}`,
      type: 'image',
      url: imageUrl
    });
    
    return response.extracted_text || '';
  }

  // Helper method for PDF processing
  async processPDF(pdfContent: string): Promise<OpenRouterResponse> {
    return this.processContent({
      content: pdfContent,
      type: 'pdf'
    });
  }
}

// Factory function for creating AI service instance
export function createAIService(apiKey?: string): AIService | null {
  if (!apiKey) {
    console.warn('OpenRouter API key not provided. AI features will be disabled.');
    return null;
  }
  return new AIService(apiKey);
}