// Microlink API Service for URL previews (Free tier support)
export interface MicrolinkResponse {
  status: string;
  data: {
    title?: string;
    description?: string;
    image?: string;
    url?: string;
    logo?: string;
    author?: string;
    date?: string;
    lang?: string;
    publisher?: string;
  };
}

export interface UrlPreview {
  title: string;
  description: string;
  image?: string | undefined;
  favicon?: string | undefined;
  author?: string | undefined;
  publishedAt?: string | undefined;
  publisher?: string | undefined;
}

export class MicrolinkService {
  private baseUrl = 'https://api.microlink.io';
  private apiKey?: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey;
  }

  async getUrlPreview(url: string): Promise<UrlPreview | null> {
    try {
      const params = new URLSearchParams({
        url: encodeURIComponent(url),
        screenshot: 'false', // Disable screenshots for faster response
        video: 'false',
        audio: 'false',
        iframe: 'false'
      });

      // Add API key if available (for higher limits)
      if (this.apiKey) {
        params.append('key', this.apiKey);
      }

      const response = await fetch(`${this.baseUrl}?${params.toString()}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Bookmarqd/1.0.0'
        }
      });

      if (!response.ok) {
        console.warn(`Microlink API error: ${response.status} ${response.statusText}`);
        return null;
      }

      const data: MicrolinkResponse = await response.json();

      if (data.status !== 'success' || !data.data) {
        console.warn('Microlink API returned unsuccessful response');
        return null;
      }

      return this.transformResponse(data.data);
    } catch (error) {
      console.error('Error fetching URL preview:', error);
      return null;
    }
  }

  private transformResponse(data: MicrolinkResponse['data']): UrlPreview {
    return {
      title: data.title || 'Untitled',
      description: data.description || '',
      image: data.image || undefined,
      favicon: data.logo || undefined,
      author: data.author || undefined,
      publishedAt: data.date || undefined,
      publisher: data.publisher || undefined
    };
  }

  // Helper method for batch URL processing
  async getMultipleUrlPreviews(urls: string[]): Promise<(UrlPreview | null)[]> {
    const promises = urls.map(url => this.getUrlPreview(url));
    return Promise.all(promises);
  }

  // Helper method to check if URL is supported
  isSupportedUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      // Exclude local URLs and unsupported protocols
      return ['http:', 'https:'].includes(urlObj.protocol) && 
             !['localhost', '127.0.0.1'].includes(urlObj.hostname);
    } catch {
      return false;
    }
  }

  // Helper method for platform-specific optimizations
  async getUrlPreviewWithPlatformHints(url: string, platform?: string): Promise<UrlPreview | null> {
    const params = new URLSearchParams({
      url: encodeURIComponent(url),
      screenshot: 'false',
      video: 'false',
      audio: 'false',
      iframe: 'false'
    });

    // Add platform-specific optimizations
    switch (platform) {
      case 'youtube':
        params.append('force', 'true');
        params.append('meta', 'false');
        break;
      case 'twitter':
        params.append('waitFor', '2000'); // Wait for dynamic content
        break;
      case 'reddit':
        params.append('headers', JSON.stringify({
          'User-Agent': 'Mozilla/5.0 (compatible; Bookmarqd/1.0)'
        }));
        break;
    }

    if (this.apiKey) {
      params.append('key', this.apiKey);
    }

    try {
      const response = await fetch(`${this.baseUrl}?${params.toString()}`);
      
      if (!response.ok) {
        return null;
      }

      const data: MicrolinkResponse = await response.json();
      
      if (data.status === 'success' && data.data) {
        return this.transformResponse(data.data);
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching platform-specific URL preview:', error);
      return null;
    }
  }
}

// Factory function for creating Microlink service instance
export function createMicrolinkService(apiKey?: string): MicrolinkService {
  return new MicrolinkService(apiKey);
}