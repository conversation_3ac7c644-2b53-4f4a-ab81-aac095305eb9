import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

export type SupabaseClientType = SupabaseClient<Database>;

let supabaseClient: SupabaseClientType | null = null;

export function createSupabaseClient(
  url: string,
  anonKey: string,
  options?: {
    autoRefreshToken?: boolean;
    persistSession?: boolean;
    detectSessionInUrl?: boolean;
  }
): SupabaseClientType {
  if (!supabaseClient) {
    supabaseClient = createClient<Database>(url, anonKey, {
      auth: {
        autoRefreshToken: options?.autoRefreshToken ?? true,
        persistSession: options?.persistSession ?? true,
        detectSessionInUrl: options?.detectSessionInUrl ?? false,
      },
    });
  }
  return supabaseClient;
}

export function getSupabaseClient(): SupabaseClientType {
  if (!supabaseClient) {
    throw new Error('Supabase client not initialized. Call createSupabaseClient first.');
  }
  return supabaseClient;
}

export const SUPABASE_CONFIG = {
  url: process.env.SUPABASE_URL || '',
  anonKey: process.env.SUPABASE_ANON_KEY || '',
  storage: {
    bookmarks: 'bookmarks',
    avatars: 'avatars',
  },
  tables: {
    folders: 'folders',
    bookmarks: 'bookmarks',
  },
} as const;