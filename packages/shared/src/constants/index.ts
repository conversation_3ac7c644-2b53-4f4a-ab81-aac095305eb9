export const BRANDING = {
  name: 'Bookmarqd',
  primaryColor: '#FF4500',
  accentColor: '#FF6B35',
  theme: 'Red/orange modern design',
} as const;

export const DEFAULT_CATEGORIES = [
  {
    name: 'Ideas',
    icon: '💡',
    description: 'Creative ideas and inspiration',
    color: '#FF4500',
  },
  {
    name: 'Goals',
    icon: '🎯', 
    description: 'Personal and professional goals',
    color: '#FF6B35',
  },
  {
    name: 'Helpful Tips',
    icon: '📝',
    description: 'Useful tips and advice', 
    color: '#FF8C42',
  },
] as const;

export const PRIORITY_PLATFORMS = [
  'youtube',
  'reddit',
  'twitter', 
  'instagram',
  'facebook',
  'pinterest',
] as const;

export const SUPPORTED_FILE_TYPES = {
  images: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  documents: ['.pdf', '.doc', '.docx', '.txt'],
  all: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.pdf', '.doc', '.docx', '.txt'],
} as const;

export const API_ENDPOINTS = {
  bookmarks: '/api/bookmarks',
  folders: '/api/folders',
  upload: '/api/upload',
  ai: '/api/ai',
  search: '/api/search',
} as const;

export const LIMITS = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxTitleLength: 200,
  maxDescriptionLength: 1000,
  maxNotesLength: 2000,
  maxTagsPerBookmark: 20,
  maxTagLength: 50,
  searchResultsPerPage: 20,
} as const;

export const PLATFORM_CONFIG = {
  youtube: {
    name: 'YouTube',
    color: '#FF0000',
    icon: '📺',
    urlPatterns: [
      /^https?:\/\/(www\.)?youtube\.com\//,
      /^https?:\/\/(www\.)?youtu\.be\//,
    ],
  },
  reddit: {
    name: 'Reddit',
    color: '#FF4500',
    icon: '🔗',
    urlPatterns: [
      /^https?:\/\/(www\.)?reddit\.com\//,
    ],
  },
  twitter: {
    name: 'X/Twitter',
    color: '#1DA1F2',
    icon: '🐦',
    urlPatterns: [
      /^https?:\/\/(www\.)?(twitter\.com|x\.com)\//,
    ],
  },
  instagram: {
    name: 'Instagram',
    color: '#E4405F',
    icon: '📸',
    urlPatterns: [
      /^https?:\/\/(www\.)?instagram\.com\//,
    ],
  },
  facebook: {
    name: 'Facebook',
    color: '#1877F2',
    icon: '👤',
    urlPatterns: [
      /^https?:\/\/(www\.)?facebook\.com\//,
    ],
  },
  pinterest: {
    name: 'Pinterest',
    color: '#BD081C',
    icon: '📌',
    urlPatterns: [
      /^https?:\/\/(www\.)?pinterest\.com\//,
    ],
  },
} as const;