-- Bookmarqd Database Schema
-- Execute this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Folders table with default categories
CREATE TABLE folders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT DEFAULT '#FF4500',
  icon TEXT DEFAULT '📁',
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookmarks table with file and URL support
CREATE TABLE bookmarks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  folder_id UUID REFERENCES folders(id) ON DELETE CASCADE NOT NULL,
  
  -- Content type: 'url', 'file', 'screenshot'
  type TEXT NOT NULL CHECK (type IN ('url', 'file', 'screenshot')),
  
  -- URL bookmark fields
  url TEXT,
  title TEXT,
  description TEXT,
  platform TEXT, -- 'youtube', 'reddit', 'twitter', 'instagram', 'facebook', 'pinterest'
  thumbnail_url TEXT,
  
  -- File bookmark fields
  file_url TEXT, -- Supabase storage URL
  file_name TEXT,
  file_type TEXT, -- 'pdf', 'image', 'document'  
  file_size INTEGER,
  
  -- AI-enhanced content
  ai_summary TEXT,
  ai_tags TEXT[], -- AI-generated tags array
  extracted_text TEXT, -- OCR from images, extracted text from PDFs
  ai_insights TEXT, -- AI analysis and insights (NotebookLM style)
  
  -- User-added data
  user_tags TEXT[],
  notes TEXT,
  is_favorite BOOLEAN DEFAULT FALSE,
  
  -- Full-text search vector
  search_vector TSVECTOR GENERATED ALWAYS AS (
    to_tsvector('english', 
      coalesce(title, '') || ' ' || 
      coalesce(description, '') || ' ' || 
      coalesce(ai_summary, '') || ' ' ||
      coalesce(extracted_text, '') || ' ' ||
      coalesce(notes, '') || ' ' ||
      array_to_string(coalesce(ai_tags, ARRAY[]::TEXT[]), ' ') || ' ' ||
      array_to_string(coalesce(user_tags, ARRAY[]::TEXT[]), ' ')
    )
  ) STORED,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX bookmarks_user_id_idx ON bookmarks(user_id);
CREATE INDEX bookmarks_folder_id_idx ON bookmarks(folder_id);
CREATE INDEX bookmarks_platform_idx ON bookmarks(platform);
CREATE INDEX bookmarks_type_idx ON bookmarks(type);
CREATE INDEX bookmarks_search_idx ON bookmarks USING GIN(search_vector);
CREATE INDEX bookmarks_created_at_idx ON bookmarks(created_at DESC);
CREATE INDEX folders_user_id_idx ON folders(user_id);

-- Enable Row Level Security
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookmarks ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Users can only access their own data
CREATE POLICY "Users can access own folders" ON folders
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can access own bookmarks" ON bookmarks  
  FOR ALL USING (auth.uid() = user_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_folders_updated_at BEFORE UPDATE ON folders
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookmarks_updated_at BEFORE UPDATE ON bookmarks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default categories for new users
CREATE OR REPLACE FUNCTION create_default_folders()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO folders (user_id, name, description, color, icon, is_default) VALUES
    (NEW.id, 'Ideas', 'Creative ideas and inspiration', '#FF4500', '💡', true),
    (NEW.id, 'Goals', 'Personal and professional goals', '#FF6B35', '🎯', true),
    (NEW.id, 'Helpful Tips', 'Useful tips and advice', '#FF8C42', '📝', true);
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to create default folders when user signs up
CREATE TRIGGER create_default_folders_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION create_default_folders();

-- Storage buckets (run these in the Storage section of Supabase dashboard)
-- Create bucket: bookmarks (public read, authenticated write)
-- Create bucket: avatars (public read, authenticated write)