{"name": "bookmarqd", "version": "1.0.0", "description": "AI-powered bookmark manager that unifies content from social media platforms into visual moodboards", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"dev": "concurrently \"npm run dev:mobile\" \"npm run dev:extension\"", "dev:mobile": "cd apps/mobile && npm run start", "dev:extension": "cd apps/extension && npm run dev", "build": "npm run build:shared && npm run build:mobile && npm run build:extension", "build:shared": "cd packages/shared && npm run build", "build:mobile": "cd apps/mobile && npm run build", "build:extension": "cd apps/extension && npm run build", "type-check": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "test": "jest", "clean": "rm -rf node_modules && rm -rf packages/*/node_modules && rm -rf apps/*/node_modules", "postinstall": "npm run build:shared", "setup:db": "node setup-database.js", "setup:env": "cp .env.example .env && echo '✅ Environment file created. Please edit .env with your credentials.'"}, "dependencies": {"@supabase/supabase-js": "^2.56.0", "dotenv": "^16.6.1", "react-native": "0.72.10"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.0.0", "eslint-plugin-react-hooks": "^4.0.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}