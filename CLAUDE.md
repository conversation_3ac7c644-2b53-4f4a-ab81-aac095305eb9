# Bookmarqd Development Guide

## Project Overview

Bookmarqd is an AI-powered bookmark manager that unifies content from social media platforms into visual moodboards. It consists of:

- **Mobile App**: React Native + Expo (iOS first)
- **Chrome Extension**: Manifest V3 with minimal popup
- **Backend**: Supabase with PostgreSQL, Auth, Storage
- **Shared Package**: TypeScript utilities and types

## Architecture Decisions

### Monorepo Structure
- Used npm workspaces for better dependency management
- Shared package contains common types, utilities, and constants
- Each app is independent but shares the same foundation

### Database Design
- PostgreSQL with full-text search using tsvectors
- Row-level security for data isolation
- Generated columns for search optimization
- Triggers for automatic timestamp updates

### AI Integration Points
- Content summarization for bookmarks
- Automatic tag generation
- OCR for images and PDFs
- Full-text search enhancement
- NotebookLM-style insights

## Development Commands

```bash
# Install all dependencies
npm install

# Start development
npm run dev

# Build everything
npm run build

# Type checking
npm run type-check

# Linting
npm run lint:fix
```

## Key Files and Structure

### Shared Package (`packages/shared/`)
- `src/types/database.ts` - Supabase database types
- `src/types/api.ts` - API request/response types
- `src/constants/index.ts` - App constants and configuration
- `src/utils/` - Platform detection, validation utilities
- `src/api/supabase.ts` - Supabase client setup

### Mobile App (`apps/mobile/`)
- `App.tsx` - Main app component with providers
- `src/navigation/` - React Navigation setup
- `src/screens/` - Screen components (auth, main, detail)
- `src/components/` - Reusable UI components
- `src/services/` - API services and Supabase integration
- `src/store/` - State management (Zustand + React Query)

### Chrome Extension (`apps/extension/`)
- `manifest.json` - Extension configuration
- `src/background/` - Service worker for context menus
- `src/content/` - Content scripts for platform detection
- `src/popup/` - React popup interface
- `webpack.config.js` - Build configuration

## Database Schema Notes

The database uses two main tables:
- `folders` - User's bookmark folders with default categories
- `bookmarks` - Bookmarks with URL, file, and AI-enhanced content

Key features:
- Generated tsvector column for full-text search
- Support for multiple content types (url, file, screenshot)
- AI fields for summaries, tags, and insights
- Platform detection and metadata storage

## Platform Detection

Priority platforms with content script injection:
1. YouTube - Video detection with thumbnail extraction
2. Reddit - Post detection with title/content
3. X/Twitter - Tweet detection and threading
4. Instagram - Post detection with image URLs
5. Facebook - Basic post detection
6. Pinterest - Pin detection with images

## AI Processing Flow

1. Content detection (URL, file upload, screenshot)
2. Platform-specific metadata extraction
3. AI processing (summary, tags, insights)
4. Full-text search vector generation
5. Storage with user-defined tags and notes

## Security Considerations

- Row-level security in Supabase
- JWT authentication
- Encrypted file storage
- No sensitive data in logs
- Content Security Policy for extension

## Build and Deployment

### Mobile App
- Uses Expo for easier iOS/Android builds
- EAS Build for production releases
- Share sheet integration for iOS

### Chrome Extension
- Webpack build process
- Manifest V3 compliance
- Content script injection for priority sites
- Chrome Web Store packaging

## Testing Strategy

- Unit tests for shared utilities
- Integration tests for API services
- E2E tests for critical user flows
- Manual testing on priority platforms

## Performance Optimizations

- React Query for efficient data fetching
- Memoized components and hooks
- Optimized database indexes
- Compressed images and efficient storage
- Background processing for AI features