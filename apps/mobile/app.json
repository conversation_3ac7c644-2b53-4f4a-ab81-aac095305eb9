{"expo": {"name": "Bookmarqd", "slug": "bookmarqd", "version": "1.0.0", "sdkVersion": "53.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#FF4500"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.bookmarqd.mobile", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to capture screenshots for bookmarks", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to save and select images for bookmarks"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FF4500"}, "package": "com.bookmarqd.mobile", "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [], "scheme": "bookmarqd"}}