{"name": "bookmarqd-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build": "expo build", "build:ios": "eas build --platform ios", "build:android": "eas build --platform android", "dev": "expo start --dev-client", "prebuild": "expo prebuild", "eject": "expo eject"}, "dependencies": {"@bookmarqd/shared": "*", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-camera-roll/camera-roll": "~5.6.0", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^6.5.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@supabase/supabase-js": "^2.38.0", "expo": "53.0.22", "expo-av": "~15.1.7", "expo-camera": "~16.1.11", "expo-constants": "~17.1.7", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-secure-store": "~14.2.4", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "metro": "^0.76.8", "metro-config": "^0.76.8", "metro-core": "^0.76.8", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-query": "^3.39.0", "zustand": "^4.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@types/react-native": "~0.72.2", "typescript": "~5.8.3"}, "private": true}