import { 
  createAIService, 
  createMicrolinkService, 
  detectPlatform, 
  extractYouTubeVideoId,
  getYouTubeThumbnail,
  AIService,
  MicrolinkService 
} from '@bookmarqd/shared';

// Initialize services with environment variables
const aiService = createAIService(process.env.OPENROUTER_API_KEY);
const microlinkService = createMicrolinkService(process.env.MICROLINK_API_KEY);

export interface ProcessedContent {
  title: string;
  description?: string;
  thumbnail_url?: string;
  platform?: string;
  ai_summary?: string;
  ai_tags?: string[];
  ai_insights?: string;
}

export class ContentService {
  private aiService: AIService | null;
  private microlinkService: MicrolinkService;

  constructor() {
    this.aiService = aiService;
    this.microlinkService = microlinkService;
  }

  async processUrl(url: string): Promise<ProcessedContent> {
    const platform = detectPlatform(url);
    const result: ProcessedContent = {
      title: url,
      platform: platform || undefined
    };

    try {
      // Get URL preview using Microlink
      const preview = await this.microlinkService.getUrlPreviewWithPlatformHints(url, platform);
      
      if (preview) {
        result.title = preview.title || url;
        result.description = preview.description;
        result.thumbnail_url = preview.image;
      }

      // Platform-specific handling
      if (platform === 'youtube') {
        const videoId = extractYouTubeVideoId(url);
        if (videoId && !result.thumbnail_url) {
          result.thumbnail_url = getYouTubeThumbnail(videoId);
        }
      }

      // AI processing if available
      if (this.aiService && (preview?.description || preview?.title)) {
        const aiResponse = await this.aiService.processContent({
          content: `${preview.title}\n${preview.description || ''}`,
          type: 'url',
          url
        });

        result.ai_summary = aiResponse.summary;
        result.ai_tags = aiResponse.tags;
        result.ai_insights = aiResponse.insights;
      }

      return result;
    } catch (error) {
      console.error('Error processing URL:', error);
      return result; // Return basic result even if processing fails
    }
  }

  async processFile(fileUri: string, fileName: string, fileType: string): Promise<ProcessedContent> {
    const result: ProcessedContent = {
      title: fileName,
    };

    try {
      // AI processing for files if available
      if (this.aiService) {
        let content = '';
        
        if (fileType === 'image') {
          // For images, we'd typically extract text using OCR
          content = `Image file: ${fileName}`;
        } else if (fileType === 'pdf') {
          // For PDFs, we'd extract text content
          content = `PDF file: ${fileName}`;
        } else {
          content = `Document file: ${fileName}`;
        }

        const aiResponse = await this.aiService.processContent({
          content,
          type: fileType as any
        });

        result.ai_summary = aiResponse.summary;
        result.ai_tags = aiResponse.tags;
        result.ai_insights = aiResponse.insights;
      }

      return result;
    } catch (error) {
      console.error('Error processing file:', error);
      return result;
    }
  }

  async processScreenshot(imageUri: string): Promise<ProcessedContent> {
    const result: ProcessedContent = {
      title: 'Screenshot',
    };

    try {
      // AI processing for screenshots if available
      if (this.aiService) {
        const aiResponse = await this.aiService.extractTextFromImage(imageUri);
        
        if (aiResponse) {
          result.ai_summary = 'Screenshot with extracted content';
          result.ai_tags = ['screenshot'];
          result.ai_insights = `Extracted text: ${aiResponse}`;
        }
      }

      return result;
    } catch (error) {
      console.error('Error processing screenshot:', error);
      return result;
    }
  }

  // Check if AI features are available
  isAIAvailable(): boolean {
    return this.aiService !== null;
  }

  // Check if URL preview is available
  isPreviewAvailable(): boolean {
    return true; // Microlink free tier is always available
  }
}

export const contentService = new ContentService();