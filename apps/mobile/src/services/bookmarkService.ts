import { supabaseClient } from './supabase';
import {
  Bookmark,
  Folder,
  CreateBookmarkRequest,
  UpdateBookmarkRequest,
  SearchBookmarksRequest,
  SearchBookmarksResponse,
  BookmarkWithFolder,
} from '@bookmarqd/shared';

export class BookmarkService {
  async getBookmarks(folderId?: string): Promise<Bookmark[]> {
    let query = supabaseClient
      .from('bookmarks')
      .select('*')
      .order('created_at', { ascending: false });

    if (folderId) {
      query = query.eq('folder_id', folderId);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  }

  async getBookmarkById(id: string): Promise<Bookmark | null> {
    const { data, error } = await supabaseClient
      .from('bookmarks')
      .select('*, folder:folders(*)')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  async createBookmark(bookmark: CreateBookmarkRequest): Promise<Bookmark> {
    const { data, error } = await supabaseClient
      .from('bookmarks')
      .insert(bookmark)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateBookmark(bookmark: UpdateBookmarkRequest): Promise<Bookmark> {
    const { id, ...updates } = bookmark;
    const { data, error } = await supabaseClient
      .from('bookmarks')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteBookmark(id: string): Promise<void> {
    const { error } = await supabaseClient
      .from('bookmarks')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  async searchBookmarks(request: SearchBookmarksRequest): Promise<SearchBookmarksResponse> {
    let query = supabaseClient
      .from('bookmarks')
      .select('*, folder:folders(*)', { count: 'exact' });

    // Apply filters
    if (request.query) {
      query = query.textSearch('search_vector', request.query);
    }

    if (request.folder_id) {
      query = query.eq('folder_id', request.folder_id);
    }

    if (request.platform) {
      query = query.eq('platform', request.platform);
    }

    if (request.type) {
      query = query.eq('type', request.type);
    }

    if (request.is_favorite !== undefined) {
      query = query.eq('is_favorite', request.is_favorite);
    }

    if (request.tags && request.tags.length > 0) {
      query = query.overlaps('user_tags', request.tags);
    }

    // Apply pagination
    const limit = request.limit || 20;
    const offset = request.offset || 0;

    query = query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) throw error;

    return {
      bookmarks: data || [],
      total: count || 0,
      has_more: (count || 0) > offset + limit,
    };
  }

  async toggleFavorite(id: string, isFavorite: boolean): Promise<void> {
    const { error } = await supabaseClient
      .from('bookmarks')
      .update({ is_favorite: isFavorite })
      .eq('id', id);

    if (error) throw error;
  }

  async uploadFile(file: File, bucket: string = 'bookmarks'): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`;
    const filePath = `${bucket}/${fileName}`;

    const { error } = await supabaseClient.storage
      .from(bucket)
      .upload(filePath, file);

    if (error) throw error;

    const { data } = supabaseClient.storage
      .from(bucket)
      .getPublicUrl(filePath);

    return data.publicUrl;
  }

  async deleteFile(url: string, bucket: string = 'bookmarks'): Promise<void> {
    // Extract file path from URL
    const urlParts = url.split('/');
    const bucketIndex = urlParts.findIndex(part => part === bucket);
    if (bucketIndex === -1) return;

    const filePath = urlParts.slice(bucketIndex + 1).join('/');

    const { error } = await supabaseClient.storage
      .from(bucket)
      .remove([filePath]);

    if (error) throw error;
  }
}

export const bookmarkService = new BookmarkService();