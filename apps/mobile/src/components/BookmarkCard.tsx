import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { Bookmark, PLATFORM_CONFIG, getPlatformConfig } from '@bookmarqd/shared';

interface BookmarkCardProps {
  bookmark: Bookmark;
  onPress: () => void;
  style?: ViewStyle;
}

export default function BookmarkCard({ bookmark, onPress, style }: BookmarkCardProps) {
  const platformConfig = bookmark.platform ? getPlatformConfig(bookmark.platform) : null;

  const getCardHeight = () => {
    // Randomize height for Pinterest-style layout
    const heights = [200, 250, 300, 220, 280];
    return heights[bookmark.id.length % heights.length];
  };

  const renderContent = () => {
    if (bookmark.type === 'url' && bookmark.thumbnail_url) {
      return (
        <Image
          source={{ uri: bookmark.thumbnail_url }}
          style={styles.thumbnail}
          contentFit="cover"
        />
      );
    }

    if (bookmark.type === 'file' && bookmark.file_type === 'image' && bookmark.file_url) {
      return (
        <Image
          source={{ uri: bookmark.file_url }}
          style={styles.thumbnail}
          contentFit="cover"
        />
      );
    }

    if (bookmark.type === 'screenshot' && bookmark.file_url) {
      return (
        <Image
          source={{ uri: bookmark.file_url }}
          style={styles.thumbnail}
          contentFit="cover"
        />
      );
    }

    // Fallback for text-only bookmarks
    return (
      <View style={[styles.textContent, { backgroundColor: platformConfig?.color || '#ddd' }]}>
        <Ionicons 
          name={getTypeIcon()} 
          size={32} 
          color="white" 
          style={styles.typeIcon} 
        />
        {bookmark.ai_summary && (
          <Text style={styles.summaryText} numberOfLines={4}>
            {bookmark.ai_summary}
          </Text>
        )}
      </View>
    );
  };

  const getTypeIcon = (): keyof typeof Ionicons.glyphMap => {
    if (bookmark.type === 'file') {
      switch (bookmark.file_type) {
        case 'pdf': return 'document-text-outline';
        case 'image': return 'image-outline';
        default: return 'document-outline';
      }
    }
    if (bookmark.type === 'screenshot') return 'camera-outline';
    return 'link-outline';
  };

  return (
    <TouchableOpacity
      style={[styles.container, { height: getCardHeight() }, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {/* Content */}
      {renderContent()}

      {/* Overlay */}
      <View style={styles.overlay}>
        {/* Platform Badge */}
        {platformConfig && (
          <View style={[styles.platformBadge, { backgroundColor: platformConfig.color }]}>
            <Text style={styles.platformText}>{platformConfig.name}</Text>
          </View>
        )}

        {/* Favorite Icon */}
        {bookmark.is_favorite && (
          <View style={styles.favoriteIcon}>
            <Ionicons name="heart" size={16} color="#ff4757" />
          </View>
        )}
      </View>

      {/* Bottom Info */}
      <View style={styles.bottomInfo}>
        <Text style={styles.title} numberOfLines={2}>
          {bookmark.title || bookmark.file_name || 'Untitled'}
        </Text>
        
        {bookmark.user_tags && bookmark.user_tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {bookmark.user_tags.slice(0, 2).map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>#{tag}</Text>
              </View>
            ))}
            {bookmark.user_tags.length > 2 && (
              <Text style={styles.moreTagsText}>+{bookmark.user_tags.length - 2}</Text>
            )}
          </View>
        )}
        
        <Text style={styles.date}>
          {new Date(bookmark.created_at).toLocaleDateString()}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  thumbnail: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  textContent: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeIcon: {
    marginBottom: 12,
  },
  summaryText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 8,
  },
  platformBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  platformText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  favoriteIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomInfo: {
    padding: 12,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    lineHeight: 18,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 8,
  },
  tag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 4,
    marginBottom: 2,
  },
  tagText: {
    fontSize: 10,
    color: '#666',
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 10,
    color: '#999',
    fontStyle: 'italic',
  },
  date: {
    fontSize: 11,
    color: '#999',
  },
});