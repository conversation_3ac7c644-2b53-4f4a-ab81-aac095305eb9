import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClient, QueryClientProvider } from 'react-query';
import 'react-native-url-polyfill/auto';

import Navigation from './src/navigation/Navigation';
import { AuthProvider } from './src/store/AuthStore';
import { initSupabase } from './src/services/supabase';

// Initialize Supabase
initSupabase();

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

export default function App() {
  return (
    <SafeAreaProvider>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <Navigation />
          <StatusBar style="light" backgroundColor="#FF4500" />
        </AuthProvider>
      </QueryClientProvider>
    </SafeAreaProvider>
  );
}