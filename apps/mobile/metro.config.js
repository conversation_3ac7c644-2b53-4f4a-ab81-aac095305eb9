const { getDefaultConfig } = require('expo/metro-config');

// Get the default Expo Metro config
const config = getDefaultConfig(__dirname, {
  // Disable the problematic serializer that requires the missing module
  serializer: {
    customSerializer: undefined,
  },
});

// Override the problematic configuration
if (config.serializer && config.serializer.customSerializer) {
  delete config.serializer.customSerializer;
}

module.exports = config;