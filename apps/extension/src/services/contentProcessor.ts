import { 
  detectPlatform, 
  extractYouTubeVideoId,
  getYouTubeThumbnail,
  createAIService,
  createMicrolinkService 
} from '@bookmarqd/shared';

export interface ProcessedBookmark {
  url: string;
  title: string;
  description?: string;
  platform?: string;
  thumbnail_url?: string;
  ai_summary?: string;
  ai_tags?: string[];
  ai_insights?: string;
}

export class ContentProcessor {
  private aiService: any;
  private microlinkService: any;

  constructor() {
    // Initialize services with stored API keys
    this.initializeServices();
  }

  private async initializeServices() {
    try {
      const storage = await chrome.storage.sync.get(['openrouter_api_key', 'microlink_api_key']);
      
      this.aiService = createAIService(storage.openrouter_api_key);
      this.microlinkService = createMicrolinkService(storage.microlink_api_key);
    } catch (error) {
      console.error('Error initializing content processor services:', error);
    }
  }

  async processUrl(url: string, title?: string): Promise<ProcessedBookmark> {
    const platform = detectPlatform(url);
    
    const result: ProcessedBookmark = {
      url,
      title: title || url,
      platform: platform || undefined
    };

    try {
      // Get URL preview using Microlink (free tier)
      if (this.microlinkService) {
        const preview = await this.microlinkService.getUrlPreviewWithPlatformHints(url, platform);
        
        if (preview) {
          result.title = preview.title || result.title;
          result.description = preview.description;
          result.thumbnail_url = preview.image;
        }
      }

      // Platform-specific handling
      if (platform === 'youtube') {
        const videoId = extractYouTubeVideoId(url);
        if (videoId && !result.thumbnail_url) {
          result.thumbnail_url = getYouTubeThumbnail(videoId);
        }
      }

      // AI processing if available
      if (this.aiService && (result.description || result.title !== url)) {
        try {
          const aiResponse = await this.aiService.processContent({
            content: `${result.title}\n${result.description || ''}`,
            type: 'url',
            url
          });

          result.ai_summary = aiResponse.summary;
          result.ai_tags = aiResponse.tags;
          result.ai_insights = aiResponse.insights;
        } catch (aiError) {
          console.warn('AI processing failed, continuing without AI features:', aiError);
        }
      }

      return result;
    } catch (error) {
      console.error('Error processing URL in extension:', error);
      return result; // Return basic result even if processing fails
    }
  }

  async processSelectedText(text: string, sourceUrl: string): Promise<ProcessedBookmark> {
    const platform = detectPlatform(sourceUrl);
    
    const result: ProcessedBookmark = {
      url: sourceUrl,
      title: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      description: text,
      platform: platform || undefined
    };

    try {
      // AI processing for selected text if available
      if (this.aiService) {
        const aiResponse = await this.aiService.processContent({
          content: text,
          type: 'text'
        });

        result.ai_summary = aiResponse.summary;
        result.ai_tags = aiResponse.tags;
        result.ai_insights = aiResponse.insights;
      }

      return result;
    } catch (error) {
      console.error('Error processing selected text:', error);
      return result;
    }
  }

  async processImage(imageUrl: string, sourceUrl: string): Promise<ProcessedBookmark> {
    const platform = detectPlatform(sourceUrl);
    
    const result: ProcessedBookmark = {
      url: sourceUrl,
      title: 'Image from ' + new URL(sourceUrl).hostname,
      thumbnail_url: imageUrl,
      platform: platform || undefined
    };

    try {
      // AI processing for image if available
      if (this.aiService) {
        const extractedText = await this.aiService.extractTextFromImage(imageUrl);
        
        if (extractedText) {
          result.description = 'Image with extracted text';
          result.ai_insights = `Extracted text: ${extractedText}`;
          result.ai_tags = ['image', 'ocr'];
        }
      }

      return result;
    } catch (error) {
      console.error('Error processing image:', error);
      return result;
    }
  }

  // Check if services are available
  async getServiceStatus(): Promise<{
    aiAvailable: boolean;
    previewAvailable: boolean;
  }> {
    await this.initializeServices();
    
    return {
      aiAvailable: this.aiService !== null,
      previewAvailable: true // Microlink free tier is always available
    };
  }
}

// Create singleton instance
export const contentProcessor = new ContentProcessor();