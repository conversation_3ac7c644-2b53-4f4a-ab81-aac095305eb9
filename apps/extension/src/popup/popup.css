/* Popup Styles */
.popup-container {
  width: 320px;
  height: 480px;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #FF4500, #FF6B35);
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 20px;
}

.logo h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.platform-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
}

.platform-emoji {
  font-size: 14px;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.url-preview {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid #e9ecef;
}

.favicon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.url-info {
  flex: 1;
  min-width: 0;
}

.url-title {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.url-domain {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  background: white;
  box-sizing: border-box;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #FF4500;
  box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.save-button {
  width: 100%;
  padding: 12px;
  background: #FF4500;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-button:hover:not(:disabled) {
  background: #FF6B35;
}

.save-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.footer {
  padding: 12px 16px;
  border-top: 1px solid #e9ecef;
  background: white;
}

.footer-button {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 0;
  transition: color 0.2s;
}

.footer-button:hover {
  color: #FF4500;
}

/* Auth Required Styles */
.auth-required {
  text-align: center;
  padding: 40px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.auth-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.auth-required h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.auth-required p {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.auth-button {
  background: #FF4500;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auth-button:hover {
  background: #FF6B35;
}