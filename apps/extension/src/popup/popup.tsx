import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { BRANDING, detectPlatform, getPlatformConfig } from '@bookmarqd/shared';
import './popup.css';

interface TabInfo {
  url: string;
  title: string;
  favIconUrl?: string;
}

interface Folder {
  id: string;
  name: string;
  icon: string;
  color: string;
}

const Popup: React.FC = () => {
  const [tabInfo, setTabInfo] = useState<TabInfo | null>(null);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [title, setTitle] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Get current tab info
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const tab = tabs[0];
      if (tab) {
        setTabInfo({
          url: tab.url || '',
          title: tab.title || '',
          favIconUrl: tab.favIconUrl,
        });
        setTitle(tab.title || '');
      }
    });

    // Check authentication status
    checkAuthStatus();
    
    // Load folders
    loadFolders();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const result = await chrome.storage.sync.get(['supabase_session']);
      setIsAuthenticated(!!result.supabase_session);
    } catch (error) {
      console.error('Error checking auth status:', error);
    }
  };

  const loadFolders = async () => {
    try {
      const result = await chrome.storage.local.get(['folders']);
      if (result.folders) {
        setFolders(result.folders);
        if (result.folders.length > 0 && !selectedFolder) {
          setSelectedFolder(result.folders[0].id);
        }
      }
    } catch (error) {
      console.error('Error loading folders:', error);
    }
  };

  const handleSave = async () => {
    if (!tabInfo || !selectedFolder) return;

    setLoading(true);
    
    try {
      const platform = detectPlatform(tabInfo.url);
      const platformConfig = platform ? getPlatformConfig(platform) : null;

      const bookmarkData = {
        url: tabInfo.url,
        title: title.trim(),
        notes: notes.trim(),
        folder_id: selectedFolder,
        type: 'url',
        platform,
        thumbnail_url: platformConfig ? undefined : tabInfo.favIconUrl,
      };

      // Send to background script to save
      chrome.runtime.sendMessage({
        action: 'saveBookmark',
        data: bookmarkData,
      }, (response) => {
        if (response.success) {
          // Show success notification
          chrome.runtime.sendMessage({
            action: 'notification',
            message: 'Bookmark saved successfully!',
          });
          window.close();
        } else {
          alert('Failed to save bookmark: ' + response.error);
        }
        setLoading(false);
      });
    } catch (error) {
      console.error('Error saving bookmark:', error);
      alert('Failed to save bookmark');
      setLoading(false);
    }
  };

  const handleLogin = () => {
    chrome.runtime.openOptionsPage();
  };

  if (!isAuthenticated) {
    return (
      <div className="popup-container">
        <div className="header">
          <div className="logo">
            <div className="logo-icon">📎</div>
            <h1>{BRANDING.name}</h1>
          </div>
        </div>
        <div className="auth-required">
          <div className="auth-icon">🔐</div>
          <h3>Sign In Required</h3>
          <p>Please sign in to save bookmarks to your account.</p>
          <button className="auth-button" onClick={handleLogin}>
            Sign In
          </button>
        </div>
      </div>
    );
  }

  const platform = tabInfo ? detectPlatform(tabInfo.url) : null;
  const platformConfig = platform ? getPlatformConfig(platform) : null;

  return (
    <div className="popup-container">
      <div className="header">
        <div className="logo">
          <div className="logo-icon">📎</div>
          <h1>{BRANDING.name}</h1>
        </div>
        {platformConfig && (
          <div className="platform-badge" style={{ backgroundColor: platformConfig.color }}>
            <span className="platform-emoji">{platformConfig.icon}</span>
            <span className="platform-name">{platformConfig.name}</span>
          </div>
        )}
      </div>

      <div className="content">
        <div className="url-preview">
          {tabInfo?.favIconUrl && (
            <img src={tabInfo.favIconUrl} alt="" className="favicon" />
          )}
          <div className="url-info">
            <div className="url-title">{tabInfo?.title}</div>
            <div className="url-domain">
              {tabInfo?.url ? new URL(tabInfo.url).hostname : ''}
            </div>
          </div>
        </div>

        <form onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
          <div className="form-group">
            <label>Title</label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Bookmark title"
              required
            />
          </div>

          <div className="form-group">
            <label>Folder</label>
            <select
              value={selectedFolder}
              onChange={(e) => setSelectedFolder(e.target.value)}
              required
            >
              <option value="">Select a folder</option>
              {folders.map((folder) => (
                <option key={folder.id} value={folder.id}>
                  {folder.icon} {folder.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label>Notes (Optional)</label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add notes or tags..."
              rows={3}
            />
          </div>

          <button
            type="submit"
            className="save-button"
            disabled={loading || !selectedFolder}
            style={{ backgroundColor: BRANDING.primaryColor }}
          >
            {loading ? 'Saving...' : 'Save Bookmark'}
          </button>
        </form>
      </div>

      <div className="footer">
        <button className="footer-button" onClick={() => chrome.runtime.openOptionsPage()}>
          Settings
        </button>
      </div>
    </div>
  );
};

// Initialize React app
const container = document.getElementById('root');
if (container) {
  const root = createRoot(container);
  root.render(<Popup />);
}