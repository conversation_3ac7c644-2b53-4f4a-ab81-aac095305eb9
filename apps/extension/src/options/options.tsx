import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';

interface ExtensionSettings {
  openrouter_api_key?: string;
  microlink_api_key?: string;
  auto_detection: boolean;
  context_menu: boolean;
}

const OptionsPage: React.FC = () => {
  const [settings, setSettings] = useState<ExtensionSettings>({
    auto_detection: true,
    context_menu: true,
  });
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const stored = await chrome.storage.sync.get([
        'openrouter_api_key',
        'microlink_api_key', 
        'auto_detection',
        'context_menu'
      ]);

      setSettings({
        openrouter_api_key: stored.openrouter_api_key || '',
        microlink_api_key: stored.microlink_api_key || '',
        auto_detection: stored.auto_detection !== false,
        context_menu: stored.context_menu !== false,
      });

      updateStatusIndicators(stored);
    } catch (error) {
      console.error('Error loading settings:', error);
      showStatus('Error loading settings', 'error');
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    setStatusMessage('');

    try {
      await chrome.storage.sync.set({
        openrouter_api_key: settings.openrouter_api_key,
        microlink_api_key: settings.microlink_api_key,
        auto_detection: settings.auto_detection,
        context_menu: settings.context_menu,
      });

      updateStatusIndicators(settings);
      showStatus('Settings saved successfully!', 'success');
      
      // Notify background script of settings change
      chrome.runtime.sendMessage({ action: 'settingsUpdated' });
    } catch (error) {
      console.error('Error saving settings:', error);
      showStatus('Error saving settings', 'error');
    } finally {
      setSaving(false);
    }
  };

  const testConnection = async () => {
    setTesting(true);
    setStatusMessage('');

    try {
      // Test OpenRouter API if key is provided
      if (settings.openrouter_api_key) {
        const response = await fetch('https://openrouter.ai/api/v1/models', {
          headers: {
            'Authorization': `Bearer ${settings.openrouter_api_key}`,
          },
        });

        if (!response.ok) {
          throw new Error('Invalid OpenRouter API key');
        }
        showStatus('OpenRouter connection successful!', 'success');
      } else {
        showStatus('No OpenRouter API key configured', 'error');
      }
    } catch (error) {
      showStatus(`Connection test failed: ${error.message}`, 'error');
    } finally {
      setTesting(false);
    }
  };

  const updateStatusIndicators = (settings: any) => {
    const aiStatus = document.getElementById('ai-status');
    const aiStatusText = document.getElementById('ai-status-text');
    
    if (aiStatus && aiStatusText) {
      if (settings.openrouter_api_key) {
        aiStatus.className = 'status-indicator enabled';
        aiStatusText.textContent = 'AI features enabled';
      } else {
        aiStatus.className = 'status-indicator disabled';
        aiStatusText.textContent = 'AI features disabled';
      }
    }
  };

  const showStatus = (message: string, type: 'success' | 'error') => {
    setStatusMessage(message);
    const statusDiv = document.createElement('div');
    statusDiv.className = `status ${type}`;
    statusDiv.textContent = message;
    
    const existing = document.querySelector('.status');
    if (existing) {
      existing.remove();
    }
    
    document.getElementById('status-message')?.appendChild(statusDiv);
    
    setTimeout(() => {
      statusDiv.remove();
    }, 5000);
  };

  const handleInputChange = (key: keyof ExtensionSettings, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return null; // This component doesn't render React content, it uses the existing HTML
};

// Initialize with plain JavaScript for better compatibility
document.addEventListener('DOMContentLoaded', () => {
  const openRouterInput = document.getElementById('openrouter-key') as HTMLInputElement;
  const microlinkInput = document.getElementById('microlink-key') as HTMLInputElement;
  const autoDetectionCheckbox = document.getElementById('auto-detection') as HTMLInputElement;
  const contextMenuCheckbox = document.getElementById('context-menu') as HTMLInputElement;
  const saveButton = document.getElementById('save-settings') as HTMLButtonElement;
  const testButton = document.getElementById('test-connection') as HTMLButtonElement;

  // Load settings
  chrome.storage.sync.get([
    'openrouter_api_key',
    'microlink_api_key',
    'auto_detection',
    'context_menu'
  ]).then((stored) => {
    if (openRouterInput) openRouterInput.value = stored.openrouter_api_key || '';
    if (microlinkInput) microlinkInput.value = stored.microlink_api_key || '';
    if (autoDetectionCheckbox) autoDetectionCheckbox.checked = stored.auto_detection !== false;
    if (contextMenuCheckbox) contextMenuCheckbox.checked = stored.context_menu !== false;
    
    updateStatusIndicators(stored);
  });

  // Save settings
  if (saveButton) {
    saveButton.addEventListener('click', async () => {
      saveButton.disabled = true;
      saveButton.textContent = 'Saving...';

      try {
        const settings = {
          openrouter_api_key: openRouterInput?.value || '',
          microlink_api_key: microlinkInput?.value || '',
          auto_detection: autoDetectionCheckbox?.checked || false,
          context_menu: contextMenuCheckbox?.checked || false,
        };

        await chrome.storage.sync.set(settings);
        
        updateStatusIndicators(settings);
        showStatus('Settings saved successfully!', 'success');
        
        // Notify background script
        chrome.runtime.sendMessage({ action: 'settingsUpdated' });
      } catch (error) {
        showStatus('Error saving settings', 'error');
      } finally {
        saveButton.disabled = false;
        saveButton.textContent = 'Save Settings';
      }
    });
  }

  // Test connection
  if (testButton) {
    testButton.addEventListener('click', async () => {
      testButton.disabled = true;
      testButton.textContent = 'Testing...';

      try {
        const apiKey = openRouterInput?.value;
        
        if (!apiKey) {
          showStatus('No OpenRouter API key provided', 'error');
          return;
        }

        const response = await fetch('https://openrouter.ai/api/v1/models', {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
          },
        });

        if (response.ok) {
          showStatus('OpenRouter connection successful!', 'success');
        } else {
          showStatus('Invalid OpenRouter API key', 'error');
        }
      } catch (error) {
        showStatus('Connection test failed', 'error');
      } finally {
        testButton.disabled = false;
        testButton.textContent = 'Test Connection';
      }
    });
  }

  function updateStatusIndicators(settings: any) {
    const aiStatus = document.getElementById('ai-status');
    const aiStatusText = document.getElementById('ai-status-text');
    
    if (aiStatus && aiStatusText) {
      if (settings.openrouter_api_key) {
        aiStatus.className = 'status-indicator enabled';
        aiStatusText.textContent = 'AI features enabled';
      } else {
        aiStatus.className = 'status-indicator disabled';
        aiStatusText.textContent = 'AI features disabled';
      }
    }
  }

  function showStatus(message: string, type: 'success' | 'error') {
    const statusDiv = document.createElement('div');
    statusDiv.className = `status ${type}`;
    statusDiv.textContent = message;
    
    const existing = document.querySelector('.status');
    if (existing) {
      existing.remove();
    }
    
    document.getElementById('status-message')?.appendChild(statusDiv);
    
    setTimeout(() => {
      statusDiv.remove();
    }, 5000);
  }
});

// Initialize React for future use
const container = document.getElementById('root');
if (container) {
  const root = createRoot(container);
  root.render(<OptionsPage />);
}