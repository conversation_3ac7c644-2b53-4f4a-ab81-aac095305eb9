<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookmarqd Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #FF4500, #FF6B35);
            color: white;
            border-radius: 12px;
        }
        
        .header h1 {
            margin: 0 0 8px 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            margin: 0 0 16px 0;
            color: #333;
            font-size: 18px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #FF4500;
            box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.1);
        }
        
        .form-group small {
            display: block;
            margin-top: 6px;
            color: #666;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .button {
            background: #FF4500;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .button:hover {
            background: #FF6B35;
        }
        
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 16px 0;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .feature-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 12px 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .status-indicator.enabled {
            background: #28a745;
        }
        
        .status-indicator.disabled {
            background: #dc3545;
        }
        
        .link {
            color: #FF4500;
            text-decoration: none;
        }
        
        .link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📎 Bookmarqd Settings</h1>
        <p>Configure your AI and preview features</p>
    </div>

    <div class="section">
        <h2>🤖 AI Features</h2>
        <div class="form-group">
            <label for="openrouter-key">OpenRouter API Key</label>
            <input 
                type="password" 
                id="openrouter-key" 
                placeholder="sk-or-v1-..." 
            />
            <small>
                Get your free API key from <a href="https://openrouter.ai" target="_blank" class="link">OpenRouter.ai</a>
                to enable AI-powered content analysis and tagging.
            </small>
        </div>
        
        <div class="feature-status">
            <div class="status-indicator" id="ai-status"></div>
            <span id="ai-status-text">AI features disabled</span>
        </div>
    </div>

    <div class="section">
        <h2>🔗 URL Previews</h2>
        <div class="form-group">
            <label for="microlink-key">Microlink API Key (Optional)</label>
            <input 
                type="password" 
                id="microlink-key" 
                placeholder="Enter API key for higher limits" 
            />
            <small>
                Optional: Add your <a href="https://microlink.io" target="_blank" class="link">Microlink.io</a> 
                API key for higher daily limits. Free tier works without an API key.
            </small>
        </div>
        
        <div class="feature-status">
            <div class="status-indicator enabled" id="preview-status"></div>
            <span>URL previews enabled (Free tier)</span>
        </div>
    </div>

    <div class="section">
        <h2>⚙️ Extension Settings</h2>
        <div class="form-group">
            <label>
                <input type="checkbox" id="auto-detection"> 
                Enable auto-detection on priority platforms
            </label>
            <small>Automatically detect content on YouTube, Reddit, Twitter, etc.</small>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="context-menu"> 
                Enable context menu shortcuts
            </label>
            <small>Add "Save to Bookmarqd" to right-click menu</small>
        </div>
    </div>

    <div class="section">
        <button class="button" id="save-settings">Save Settings</button>
        <button class="button" id="test-connection" style="background: #6c757d; margin-left: 8px;">Test Connection</button>
        
        <div id="status-message"></div>
    </div>

    <div id="root"></div>
</body>
</html>