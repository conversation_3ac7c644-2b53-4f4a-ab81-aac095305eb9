import { PRIORITY_PLATFORMS, detectPlatform } from '@bookmarqd/shared';

// Context menu setup
chrome.runtime.onInstalled.addListener(() => {
  // Create context menu for saving links
  chrome.contextMenus.create({
    id: 'save-to-bookmarqd',
    title: 'Save to Bookmarqd',
    contexts: ['page', 'link', 'selection'],
  });

  // Create context menu for saving images
  chrome.contextMenus.create({
    id: 'save-image-to-bookmarqd',
    title: 'Save image to Bookmarqd',
    contexts: ['image'],
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (!tab?.id) return;

  const data = {
    url: info.linkUrl || info.srcUrl || tab.url || '',
    title: info.selectionText || tab.title || '',
    type: info.srcUrl ? 'image' : 'url',
    platform: detectPlatform(info.linkUrl || tab.url || ''),
  };

  // Send message to content script to show save dialog
  chrome.tabs.sendMessage(tab.id, {
    action: 'showSaveDialog',
    data,
  });
});

// Handle tab updates to detect priority platforms
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const platform = detectPlatform(tab.url);
    
    if (platform && PRIORITY_PLATFORMS.includes(platform)) {
      // Inject auto-detection for priority platforms
      chrome.tabs.sendMessage(tabId, {
        action: 'enableAutoDetection',
        platform,
      });
    }
  }
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.action) {
    case 'getTabInfo':
      if (sender.tab) {
        sendResponse({
          url: sender.tab.url,
          title: sender.tab.title,
          favIconUrl: sender.tab.favIconUrl,
        });
      }
      break;
      
    case 'openOptions':
      chrome.runtime.openOptionsPage();
      break;
      
    case 'notification':
      // Note: notifications permission needed for this feature
      console.log('Notification:', message.message);
      break;
  }
  
  return true; // Keep message channel open for async response
});

// Badge management  
function updateBadge(tabId: number, count: number) {
  chrome.action.setBadgeText({
    tabId,
    text: count > 0 ? count.toString() : '',
  });
  
  chrome.action.setBadgeBackgroundColor({
    tabId,
    color: '#FF4500',
  });
}