/* Bookmarqd Content Script Styles */
#bookmarqd-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.bookmarqd-dialog {
  background: white;
  border-radius: 12px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.bookmarqd-header {
  background: #FF4500;
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bookmarqd-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.bookmarqd-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bookmarqd-close:hover {
  opacity: 0.7;
}

.bookmarqd-content {
  padding: 20px;
}

.bookmarqd-content input,
.bookmarqd-content textarea,
.bookmarqd-content select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 12px;
  font-family: inherit;
  box-sizing: border-box;
}

.bookmarqd-content textarea {
  height: 80px;
  resize: vertical;
}

.bookmarqd-save {
  background: #FF4500;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;
}

.bookmarqd-save:hover {
  background: #FF6B35;
}

/* Detection indicators */
.bookmarqd-detected {
  position: relative;
}

.bookmarqd-detected::after {
  content: '📎';
  position: absolute;
  top: 4px;
  right: 4px;
  background: #FF4500;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  z-index: 1000;
}