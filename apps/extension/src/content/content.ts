import { detectPlatform, PLATFORM_CONFIG } from '@bookmarqd/shared';

// Track detected content on priority platforms
let detectedContent: Array<{
  url: string;
  title: string;
  thumbnail?: string;
  type: string;
}> = [];

// Initialize content detection
function initContentDetection() {
  const url = window.location.href;
  const platform = detectPlatform(url);
  
  if (!platform) return;
  
  console.log(`Bookmarqd: Initialized detection for ${platform}`);
  
  switch (platform) {
    case 'youtube':
      detectYouTubeContent();
      break;
    case 'reddit':
      detectRedditContent();
      break;
    case 'twitter':
      detectTwitterContent();
      break;
    case 'instagram':
      detectInstagramContent();
      break;
    case 'facebook':
      detectFacebookContent();
      break;
    case 'pinterest':
      detectPinterestContent();
      break;
  }
}

// YouTube content detection
function detectYouTubeContent() {
  // Watch for video page changes
  const observer = new MutationObserver(() => {
    const videoElement = document.querySelector('video');
    const titleElement = document.querySelector('#title h1, .title');
    
    if (videoElement && titleElement) {
      const videoId = extractVideoId(window.location.href);
      if (videoId) {
        addDetectedContent({
          url: window.location.href,
          title: titleElement.textContent?.trim() || '',
          thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
          type: 'video',
        });
      }
    }
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

// Reddit content detection
function detectRedditContent() {
  const observer = new MutationObserver(() => {
    const posts = document.querySelectorAll('[data-testid="post-container"]');
    
    posts.forEach(post => {
      const titleElement = post.querySelector('h3');
      const linkElement = post.querySelector('a[data-testid="post-title"]');
      
      if (titleElement && linkElement) {
        const href = linkElement.getAttribute('href');
        if (href && !href.includes('bookmarqd-detected')) {
          linkElement.setAttribute('href', href + '?bookmarqd-detected=true');
          
          addDetectedContent({
            url: href.startsWith('/') ? `https://reddit.com${href}` : href,
            title: titleElement.textContent?.trim() || '',
            type: 'post',
          });
        }
      }
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

// Twitter/X content detection
function detectTwitterContent() {
  const observer = new MutationObserver(() => {
    const tweets = document.querySelectorAll('article[data-testid="tweet"]');
    
    tweets.forEach(tweet => {
      if (tweet.hasAttribute('data-bookmarqd-detected')) return;
      tweet.setAttribute('data-bookmarqd-detected', 'true');
      
      const textElement = tweet.querySelector('[data-testid="tweetText"]');
      const timeElement = tweet.querySelector('time');
      
      if (textElement && timeElement) {
        const href = timeElement.closest('a')?.getAttribute('href');
        if (href) {
          addDetectedContent({
            url: href.startsWith('/') ? `https://x.com${href}` : href,
            title: textElement.textContent?.trim() || '',
            type: 'tweet',
          });
        }
      }
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

// Instagram content detection
function detectInstagramContent() {
  const observer = new MutationObserver(() => {
    const posts = document.querySelectorAll('article');
    
    posts.forEach(post => {
      if (post.hasAttribute('data-bookmarqd-detected')) return;
      post.setAttribute('data-bookmarqd-detected', 'true');
      
      const imageElement = post.querySelector('img');
      const linkElement = post.querySelector('a[href*="/p/"]');
      
      if (imageElement && linkElement) {
        const href = linkElement.getAttribute('href');
        if (href) {
          addDetectedContent({
            url: href.startsWith('/') ? `https://instagram.com${href}` : href,
            title: imageElement.getAttribute('alt') || 'Instagram Post',
            thumbnail: imageElement.getAttribute('src') || undefined,
            type: 'post',
          });
        }
      }
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

// Facebook content detection
function detectFacebookContent() {
  // Facebook detection is more complex due to dynamic loading
  // This is a basic implementation
  const observer = new MutationObserver(() => {
    const posts = document.querySelectorAll('[role="article"]');
    
    posts.forEach(post => {
      if (post.hasAttribute('data-bookmarqd-detected')) return;
      post.setAttribute('data-bookmarqd-detected', 'true');
      
      const textElements = post.querySelectorAll('[data-ad-preview="message"]');
      if (textElements.length > 0) {
        addDetectedContent({
          url: window.location.href,
          title: textElements[0]?.textContent?.trim() || 'Facebook Post',
          type: 'post',
        });
      }
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

// Pinterest content detection
function detectPinterestContent() {
  const observer = new MutationObserver(() => {
    const pins = document.querySelectorAll('[data-test-id="pin"]');
    
    pins.forEach(pin => {
      if (pin.hasAttribute('data-bookmarqd-detected')) return;
      pin.setAttribute('data-bookmarqd-detected', 'true');
      
      const imageElement = pin.querySelector('img');
      const linkElement = pin.querySelector('a');
      
      if (imageElement && linkElement) {
        const href = linkElement.getAttribute('href');
        if (href) {
          addDetectedContent({
            url: href.startsWith('/') ? `https://pinterest.com${href}` : href,
            title: imageElement.getAttribute('alt') || 'Pinterest Pin',
            thumbnail: imageElement.getAttribute('src') || undefined,
            type: 'pin',
          });
        }
      }
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

// Helper functions
function extractVideoId(url: string): string | null {
  const match = url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
  return match ? match[1] || null : null;
}

function addDetectedContent(content: typeof detectedContent[0]) {
  // Avoid duplicates
  const exists = detectedContent.some(item => item.url === content.url);
  if (!exists) {
    detectedContent.push(content);
    
    // Update badge count
    chrome.runtime.sendMessage({
      action: 'updateBadge',
      count: detectedContent.length,
    });
  }
}

// Handle messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.action) {
    case 'enableAutoDetection':
      initContentDetection();
      break;
      
    case 'showSaveDialog':
      showSaveDialog(message.data);
      break;
      
    case 'getDetectedContent':
      sendResponse({ content: detectedContent });
      break;
  }
  
  return true;
});

// Show save dialog overlay
function showSaveDialog(data: any) {
  // Create overlay
  const overlay = document.createElement('div');
  overlay.id = 'bookmarqd-overlay';
  overlay.innerHTML = `
    <div class="bookmarqd-dialog">
      <div class="bookmarqd-header">
        <h3>Save to Bookmarqd</h3>
        <button class="bookmarqd-close">&times;</button>
      </div>
      <div class="bookmarqd-content">
        <input type="text" placeholder="Title" value="${data.title}" />
        <textarea placeholder="Notes (optional)"></textarea>
        <select>
          <option>Select folder...</option>
        </select>
        <button class="bookmarqd-save">Save Bookmark</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(overlay);
  
  // Handle close
  overlay.querySelector('.bookmarqd-close')?.addEventListener('click', () => {
    overlay.remove();
  });
  
  // Handle save
  overlay.querySelector('.bookmarqd-save')?.addEventListener('click', () => {
    // Send save request to extension
    chrome.runtime.sendMessage({
      action: 'saveBookmark',
      data: {
        ...data,
        title: (overlay.querySelector('input') as HTMLInputElement).value,
        notes: (overlay.querySelector('textarea') as HTMLTextAreaElement).value,
      },
    });
    
    overlay.remove();
  });
}

// Initialize on page load
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initContentDetection);
} else {
  initContentDetection();
}