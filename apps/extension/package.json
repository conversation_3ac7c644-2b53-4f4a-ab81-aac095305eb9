{"name": "bookmarqd-extension", "version": "1.0.0", "description": "Bookmarqd Chrome Extension - Quick bookmark saving", "main": "dist/background.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@bookmarqd/shared": "*", "@supabase/supabase-js": "^2.38.0"}, "devDependencies": {"@types/chrome": "^0.0.246", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "style-loader": "^3.3.0", "ts-loader": "^9.4.0", "typescript": "^5.0.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "private": true}