# Bookmarqd

AI-powered bookmark manager that unifies content from social media platforms into visual moodboards.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- Supabase account and project
- Expo CLI (for mobile development)
- Chrome browser (for extension testing)

### Installation

```bash
# Install dependencies
npm install

# Build shared package
npm run build:shared

# Set up environment variables
cp .env.example .env
# Edit .env with your Supabase credentials
```

### Environment Setup

Create a `.env` file in the root directory:

```env
# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# For mobile app
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# AI Features (Optional)
GEMINI_API_KEY=your_gemini_api_key
```

### Database Setup

1. Create a new Supabase project
2. Run the SQL schema from `supabase-schema.sql` in your Supabase SQL Editor
3. Create storage buckets:
   - `bookmarks` (public read, authenticated write)
   - `avatars` (public read, authenticated write)

## 📱 Development

### Mobile App (iOS/Android)

```bash
# Start the mobile app
npm run dev:mobile

# Or specifically
cd apps/mobile
npm run start
```

### Chrome Extension

```bash
# Build the extension
npm run dev:extension

# Or for production build
npm run build:extension
```

To load the extension in Chrome:
1. Go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked"
4. Select the `apps/extension/dist` folder

### Development Commands

```bash
# Start all development servers
npm run dev

# Build everything
npm run build

# Type check all packages
npm run type-check

# Lint and fix
npm run lint:fix

# Run tests
npm run test

# Clean all build artifacts
npm run clean
```

## 🏗️ Architecture

### Monorepo Structure

```
bookmarqd/
├── packages/
│   └── shared/              # Shared types, utilities, constants
├── apps/
│   ├── mobile/             # React Native + Expo app
│   └── extension/          # Chrome extension
├── supabase-schema.sql     # Database schema
└── package.json           # Workspace configuration
```

### Tech Stack

- **Monorepo**: npm workspaces with TypeScript
- **Mobile**: React Native with Expo (iOS first)
- **Extension**: Chrome Manifest V3 with React
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **AI**: Gemini Flash-Lite via OpenRouter
- **Previews**: Microlink API

### Key Features

#### Mobile App
- 📱 iOS share sheet integration
- 📸 Screenshot capture and upload
- 📄 PDF and document picker with AI processing
- 🎨 Pinterest-style moodboard grid layout
- 🔍 AI-powered full-text search
- 📁 Folder management with default categories

#### Chrome Extension
- 🔥 320px wide popup for quick bookmark addition
- 🤖 Auto-detect and save from priority sites
- 🎯 Right-click "Save to Bookmarqd" context menu
- 🔐 Login using mobile app credentials
- 📁 Quick folder picker interface

#### AI Features
- 📝 Content summarization and insight generation
- 🏷️ Automatic tag suggestions
- 🔍 AI-powered search across all content
- 👁️ OCR text extraction from images
- 📄 PDF text extraction and processing
- 🧠 NotebookLM-style content connections

## 🌐 Priority Platforms

1. **YouTube** - Video bookmarks with transcript extraction
2. **Reddit** - Discussion threads and comments
3. **X/Twitter** - Tweets and threads
4. **Instagram** - Posts and stories
5. **Facebook** - Posts and articles
6. **Pinterest** - Pins and boards

## 📁 Default Categories

- **Ideas** 💡 - Creative ideas and inspiration
- **Goals** 🎯 - Personal and professional goals  
- **Helpful Tips** 📝 - Useful tips and advice

## 🎨 Branding

- **Primary Color**: #FF4500 (Orange Red)
- **Accent Color**: #FF6B35 (Orange)
- **Theme**: Modern red/orange gradient design

## 🚢 Deployment

### Mobile App

```bash
# iOS
cd apps/mobile
eas build --platform ios

# Android  
eas build --platform android
```

### Chrome Extension

```bash
# Build for production
npm run build:extension

# Package for Chrome Web Store
cd apps/extension/dist
zip -r bookmarqd-extension.zip *
```

## 🔒 Security Features

- Row-level security (RLS) in Supabase
- Encrypted file storage
- Secure authentication with JWT tokens
- HTTPS-only communication
- No sensitive data logging

## 📊 Performance

- Sub-second full-text search
- Real-time sync between devices
- Offline support for basic functionality  
- Optimized for thousands of bookmarks per user
- Efficient image and file processing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and feature requests, please use the GitHub issue tracker.

---

Built with ❤️ using React Native, Chrome Extensions, and Supabase.