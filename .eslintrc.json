{"root": true, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:react-native/all"], "plugins": ["@typescript-eslint", "react", "react-hooks", "react-native"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"es2022": true, "node": true, "browser": true, "react-native/react-native": true}, "settings": {"react": {"version": "detect"}}, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react-native/no-unused-styles": "error", "react-native/split-platform-components": "error"}, "overrides": [{"files": ["apps/extension/**/*"], "env": {"browser": true, "webextensions": true}, "rules": {"react-native/no-unused-styles": "off", "react-native/split-platform-components": "off"}}]}