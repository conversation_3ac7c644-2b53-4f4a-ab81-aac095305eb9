{"projectName": "Bookmarqd", "description": "AI-powered bookmark manager that unifies content from social media platforms into visual moodboards", "techStack": {"monorepo": "Yarn workspaces with TypeScript", "mobile": "React Native with Expo (iOS first)", "extension": "Chrome Manifest V3 (minimal popup)", "backend": "Supabase (database, auth, storage, edge functions)", "ai": "Gemini Flash-Lite via OpenRouter", "previews": "Microlink API", "packageManager": "npm/npx", "platform": "macOS development"}, "branding": {"name": "Bookmarqd", "primaryColor": "#FF4500", "accentColor": "#FF6B35", "theme": "Red/orange modern design"}, "features": {"platforms": ["iOS mobile app", "Chrome extension"], "prioritySites": ["YouTube", "Reddit", "X/Twitter", "Instagram", "Facebook", "Pinterest"], "defaultCategories": ["Ideas", "Goals", "Helpful Tips"], "aiFeatures": ["AI-powered search across all bookmarks", "Content summarization and insights", "PDF text extraction and OCR", "NotebookLM-style knowledge base"], "fileSupport": ["URL bookmarks", "Screenshots", "PDFs", "Images"], "mobileFeatures": ["Share sheet integration", "Camera for screenshots", "File picker for documents", "Visual moodboard layout"], "extensionFeatures": ["Minimal popup for quick adds", "Content scripts for priority sites", "Right-click context menu", "Login with mobile credentials"]}, "implementation": {"priorities": ["Monorepo structure with TypeScript", "Supabase database setup with schema", "Mobile app core (auth, folders, bookmarks)", "File upload with AI processing", "Chrome extension with content scripts", "AI-powered search implementation", "Real-time sync between platforms", "Visual moodboard interface"]}}