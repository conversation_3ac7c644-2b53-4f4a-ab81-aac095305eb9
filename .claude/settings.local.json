{"permissions": {"allow": ["<PERSON><PERSON>(claude mcp:*)", "Bash(npm install)", "Bash(npm run build:*)", "Bash(npm run type-check:*)", "Bash(tsc --noEmit)", "Bash(npx tsc:*)", "Bash(npm run lint)", "mcp__supabase__list_tables", "mcp__supabase__list_migrations", "Bash(node:*)", "Bash(npm --version)", "Bash(npx expo@latest install --fix)", "Bash(npx expo:*)", "Bash(rm:*)", "Bash(npm install:*)", "mcp__brave-search__brave_web_search"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}