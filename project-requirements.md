# Bookmarqd - Project Requirements

## Overview
AI-powered bookmark manager that unifies content from social media platforms into visual moodboards with file upload capabilities.

## Core Features

### Mobile App (iOS First)
- **Onboarding**: Red/orange branded flow with authentication
- **Share Integration**: iOS share sheet for receiving URLs from other apps
- **Camera**: Screenshot capture and upload
- **File Upload**: PDF and document picker with AI processing
- **Visual Interface**: Pinterest-style moodboard grid layout
- **AI Search**: Full-text search across all content types
- **Folder Management**: Create, edit, organize with default categories

### Chrome Extension (Minimal)
- **Popup**: 320px wide interface for quick bookmark addition
- **Content Scripts**: Auto-detect and save from priority sites
- **Context Menu**: Right-click "Save to Bookmarqd" option
- **Authentication**: Login using mobile app credentials
- **Folder Selection**: Quick folder picker interface

### Backend (Supabase)
- **Authentication**: Email/password and social login
- **Database**: PostgreSQL with full-text search
- **Storage**: File uploads for images and PDFs
- **Real-time**: Live sync between mobile and extension
- **Edge Functions**: AI processing and content analysis

### AI Features (Gemini Flash-Lite)
- **Content Analysis**: Summarization and insight generation
- **Tagging**: Automatic tag suggestions
- **Search**: AI-powered search across all content
- **OCR**: Text extraction from images
- **PDF Processing**: Text extraction from documents
- **Knowledge Base**: NotebookLM-style content connections

## Priority Platforms
1. YouTube (video bookmarks, transcript extraction)
2. Reddit (discussion threads, comments)
3. X/Twitter (tweets, threads)
4. Instagram (posts, stories)
5. Facebook (posts, articles)
6. Pinterest (pins, boards)

## Default Categories
- **Ideas** 💡 (Creative ideas and inspiration)
- **Goals** 🎯 (Personal and professional goals)
- **Helpful Tips** 📝 (Useful tips and advice)

## Technical Requirements
- **Cross-platform sync**: Real-time data synchronization
- **Offline support**: Basic functionality without internet
- **File processing**: PDF text extraction, image OCR
- **Search performance**: Sub-second full-text search
- **Security**: Row-level security, encrypted file storage
- **Scalability**: Support for thousands of bookmarks per user